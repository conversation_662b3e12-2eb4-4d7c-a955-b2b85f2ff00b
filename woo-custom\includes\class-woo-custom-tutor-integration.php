<?php
/**
 * Tutor LMS Integration Class
 * 
 * Handles WooCommerce product page "Add to Cart" button visibility
 * based on user's purchase history for Tutor LMS courses
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Tutor Integration Class
 */
class WooCustom_Tutor_Integration {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Hook into WooCommerce single product page
        add_action('woocommerce_single_product_summary', array($this, 'maybe_hide_add_to_cart_button'), 25);
        
        // Alternative hook for different themes
        add_filter('woocommerce_is_purchasable', array($this, 'filter_product_purchasable'), 10, 2);
        
        // Add custom CSS to hide button when needed
        add_action('wp_head', array($this, 'add_custom_css'));
        
        // Debug hook (only in debug mode)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_action('wp_footer', array($this, 'debug_product_info'));
        }
    }
    
    /**
     * Check if Tutor LMS is active
     */
    private function is_tutor_active() {
        return function_exists('tutor_utils') || class_exists('TUTOR');
    }
    
    /**
     * Check if current product is a Tutor course product
     */
    private function is_tutor_course_product($product_id = null) {
        if (!$this->is_tutor_active()) {
            return false;
        }
        
        if (!$product_id) {
            global $product;
            if (!$product) {
                return false;
            }
            $product_id = $product->get_id();
        }
        
        // Check if product has Tutor course meta
        $is_tutor_product = get_post_meta($product_id, '_tutor_product', true);
        return $is_tutor_product === 'yes';
    }
    
    /**
     * Check if user has any order (completed or pending) for this product
     * This matches Tutor LMS behavior where pending orders also create enrollments
     */
    private function user_has_order_for_product($user_id, $product_id) {
        if (!$user_id || !$product_id) {
            return false;
        }

        global $wpdb;

        // Check if HPOS (High Performance Order Storage) is enabled
        $hpos_enabled = class_exists('Automattic\WooCommerce\Internal\DataStores\Orders\OrdersTableDataStore');

        if ($hpos_enabled && $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_orders'") === $wpdb->prefix . 'wc_orders') {
            // Use HPOS tables - check for completed, processing, or pending orders
            $query = "
                SELECT COUNT(*)
                FROM {$wpdb->prefix}wc_orders o
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON o.id = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
                WHERE o.customer_id = %d
                AND o.status IN ('wc-completed', 'wc-processing', 'wc-pending')
                AND oim.meta_key = '_product_id'
                AND oim.meta_value = %d
            ";

            $count = $wpdb->get_var($wpdb->prepare($query, $user_id, $product_id));
        } else {
            // Use legacy posts table - check for completed, processing, or pending orders
            $query = "
                SELECT COUNT(*)
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE pm.meta_key = '_customer_user'
                AND pm.meta_value = %d
                AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-pending')
                AND p.post_type = 'shop_order'
                AND oim.meta_key = '_product_id'
                AND oim.meta_value = %d
            ";

            $count = $wpdb->get_var($wpdb->prepare($query, $user_id, $product_id));
        }

        return $count > 0;
    }

    /**
     * Check if user has completed order for this product (legacy method)
     */
    private function user_has_completed_order($user_id, $product_id) {
        if (!$user_id || !$product_id) {
            return false;
        }

        // Use WooCommerce's built-in function if available
        if (function_exists('wc_customer_bought_product')) {
            return wc_customer_bought_product('', $user_id, $product_id);
        }

        return false;
    }
    
    /**
     * Check if user has any enrollment (completed or pending) for this course
     * This matches Tutor LMS behavior where pending enrollments also restrict access
     */
    private function user_is_enrolled_in_course($user_id, $product_id) {
        if (!$this->is_tutor_active() || !function_exists('tutor_utils')) {
            return false;
        }

        // Get course ID from product
        $course_id = $this->get_course_id_from_product($product_id);
        if (!$course_id) {
            return false;
        }

        // Check if user has any enrollment (including pending) - this is the key change!
        $is_enrolled = tutor_utils()->is_enrolled($course_id, $user_id, false); // false = don't require completed status
        return !empty($is_enrolled);
    }
    
    /**
     * Get course ID from product ID
     */
    private function get_course_id_from_product($product_id) {
        if (!$this->is_tutor_active()) {
            return false;
        }

        // Use Tutor's built-in function if available
        if (function_exists('tutor_utils')) {
            $course_relation = tutor_utils()->product_belongs_with_course($product_id);
            if ($course_relation && isset($course_relation->post_id)) {
                return $course_relation->post_id;
            }
        }

        global $wpdb;

        // Fallback: Query to find course associated with this product
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta}
             WHERE meta_key = '_tutor_course_product_id'
             AND meta_value = %d",
            $product_id
        ));

        return $course_id;
    }
    
    /**
     * Check if user should see add to cart button
     * This follows Tutor LMS logic: hide button if user has any order or enrollment
     */
    private function should_show_add_to_cart($product_id = null) {
        // If user is not logged in, show button
        if (!is_user_logged_in()) {
            return true;
        }

        if (!$product_id) {
            global $product;
            if (!$product) {
                return true;
            }
            $product_id = $product->get_id();
        }

        // Only apply to Tutor course products
        if (!$this->is_tutor_course_product($product_id)) {
            return true;
        }

        $user_id = get_current_user_id();

        // Primary check: Does user have any order (pending, processing, or completed)?
        $has_any_order = $this->user_has_order_for_product($user_id, $product_id);

        // Secondary check: Is user enrolled (including pending enrollments)?
        $is_enrolled = $this->user_is_enrolled_in_course($user_id, $product_id);

        // Hide button if user has any order OR any enrollment (including pending)
        return !($has_any_order || $is_enrolled);
    }
    
    /**
     * Maybe hide add to cart button
     */
    public function maybe_hide_add_to_cart_button() {
        if (!$this->should_show_add_to_cart()) {
            // Remove add to cart button
            remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
            
            // Add custom message instead
            add_action('woocommerce_single_product_summary', array($this, 'show_already_purchased_message'), 30);
        }
    }
    
    /**
     * Filter product purchasable status
     */
    public function filter_product_purchasable($purchasable, $product) {
        if (!$purchasable) {
            return $purchasable;
        }
        
        $product_id = $product->get_id();
        
        // Only apply to Tutor course products
        if (!$this->is_tutor_course_product($product_id)) {
            return $purchasable;
        }
        
        return $this->should_show_add_to_cart($product_id);
    }
    
    /**
     * Show already purchased message
     */
    public function show_already_purchased_message() {
        global $product;

        if (!$product) {
            return;
        }

        $user_id = get_current_user_id();
        $product_id = $product->get_id();
        $course_id = $this->get_course_id_from_product($product_id);

        // Check if user has completed order or completed enrollment
        $has_completed_order = $this->user_has_completed_order($user_id, $product_id);
        $has_completed_enrollment = false;

        if ($course_id && function_exists('tutor_utils')) {
            $completed_enrollment = tutor_utils()->is_enrolled($course_id, $user_id, true); // true = only completed
            $has_completed_enrollment = !empty($completed_enrollment);
        }

        echo '<div class="woo-custom-already-purchased">';

        // Show different messages based on status
        if ($has_completed_order || $has_completed_enrollment) {
            // User has completed purchase/enrollment - show access message
            echo '<p class="already-purchased-message">';
            echo '<span class="dashicons dashicons-yes-alt"></span> ';
            echo esc_html__('Bu kursa zaten sahipsiniz.', 'woo-custom');
            echo '</p>';

            // Show course access button only for completed purchases
            if ($course_id) {
                $course_url = get_permalink($course_id);
                if ($course_url && $course_url !== '#') {
                    echo '<a href="' . esc_url($course_url) . '" class="button alt course-access-button">';
                    echo esc_html__('Kursa Git', 'woo-custom');
                    echo '</a>';
                }
            }
        } else {
            // User has pending order/enrollment - show pending message
            echo '<p class="already-purchased-message" style="color: #f39c12;">';
            echo '<span class="dashicons dashicons-clock"></span> ';
            echo esc_html__('Bu kurs için siparişiniz işleniyor.', 'woo-custom');
            echo '</p>';
            echo '<p style="font-size: 14px; color: #666; margin: 10px 0 0 0;">';
            echo esc_html__('Ödemeniz onaylandıktan sonra kursa erişebileceksiniz.', 'woo-custom');
            echo '</p>';
        }

        echo '</div>';
    }
    
    /**
     * Add custom CSS
     */
    public function add_custom_css() {
        ?>
        <style type="text/css">
        .woo-custom-already-purchased {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .already-purchased-message {
            color: #28a745;
            font-weight: 600;
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        
        .already-purchased-message .dashicons {
            color: #28a745;
            font-size: 18px;
            vertical-align: middle;
            margin-right: 5px;
        }
        
        .course-access-button {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: white !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .course-access-button:hover {
            background-color: #218838 !important;
            border-color: #1e7e34 !important;
            color: white !important;
        }
        </style>
        <?php
    }
    
    /**
     * Debug product info (only in debug mode)
     */
    public function debug_product_info() {
        if (!is_product()) {
            return;
        }

        global $product;
        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $user_id = get_current_user_id();
        $course_id = $this->get_course_id_from_product($product_id);

        echo '<!-- Woo Custom Tutor Integration Debug -->';
        echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #000; color: #fff; padding: 15px; font-size: 11px; z-index: 9999; max-width: 350px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">';
        echo '<strong style="color: #4CAF50;">🔧 Tutor Integration Debug</strong><br><br>';
        echo '<strong>Basic Info:</strong><br>';
        echo '• Product ID: ' . $product_id . '<br>';
        echo '• User ID: ' . ($user_id ?: 'Not logged in') . '<br>';
        echo '• Course ID: ' . ($course_id ?: 'Not found') . '<br><br>';

        echo '<strong>Checks:</strong><br>';
        echo '• Is Tutor Product: ' . ($this->is_tutor_course_product($product_id) ? '✅ Yes' : '❌ No') . '<br>';
        echo '• Tutor Active: ' . ($this->is_tutor_active() ? '✅ Yes' : '❌ No') . '<br>';

        if ($user_id) {
            echo '• Has Any Order: ' . ($this->user_has_order_for_product($user_id, $product_id) ? '✅ Yes' : '❌ No') . '<br>';
            echo '• Has Completed Order: ' . ($this->user_has_completed_order($user_id, $product_id) ? '✅ Yes' : '❌ No') . '<br>';
            echo '• Is Enrolled (any status): ' . ($this->user_is_enrolled_in_course($user_id, $product_id) ? '✅ Yes' : '❌ No') . '<br>';

            // Also check completed enrollment separately
            if (function_exists('tutor_utils')) {
                $course_id = $this->get_course_id_from_product($product_id);
                if ($course_id) {
                    $completed_enrollment = tutor_utils()->is_enrolled($course_id, $user_id, true); // true = only completed
                    echo '• Is Enrolled (completed only): ' . ($completed_enrollment ? '✅ Yes' : '❌ No') . '<br>';
                }
            }
        }

        echo '<br><strong>Result:</strong><br>';
        echo '• Should Show Button: ' . ($this->should_show_add_to_cart($product_id) ? '✅ Yes' : '❌ No') . '<br>';

        // Additional meta info
        $tutor_product_meta = get_post_meta($product_id, '_tutor_product', true);
        echo '<br><strong>Meta Data:</strong><br>';
        echo '• _tutor_product: ' . ($tutor_product_meta ?: 'Not set') . '<br>';

        if (function_exists('wc_customer_bought_product') && $user_id) {
            echo '• WC bought check: ' . (wc_customer_bought_product('', $user_id, $product_id) ? '✅ Yes' : '❌ No') . '<br>';
        }

        echo '</div>';
    }
}
