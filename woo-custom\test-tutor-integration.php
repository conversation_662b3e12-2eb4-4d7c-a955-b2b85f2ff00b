<?php
/**
 * Test file for Tutor LMS Integration
 * 
 * This file can be used to test the Tutor LMS integration functionality
 * Access via: /wp-content/plugins/woo-custom/test-tutor-integration.php
 * 
 * @package WooCustom
 * @since 1.0.0
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Bu say<PERSON>ya eri<PERSON>im yetkiniz yok.');
}

// Get the integration class
if (class_exists('WooCustom_Tutor_Integration')) {
    $integration = WooCustom_Tutor_Integration::instance();
} else {
    wp_die('Tutor Integration sınıfı bulunamadı. Eklenti aktif mi?');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Tutor LMS Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .status-yes { color: #28a745; font-weight: bold; }
        .status-no { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 Tutor LMS Integration Test</h1>
    
    <div class="test-section info">
        <h2>Sistem Durumu</h2>
        <?php
        echo '<div class="test-result">';
        echo '<strong>WordPress Sürümü:</strong> ' . get_bloginfo('version') . '<br>';
        echo '<strong>WooCommerce Aktif:</strong> ' . (class_exists('WooCommerce') ? '<span class="status-yes">✅ Evet</span>' : '<span class="status-no">❌ Hayır</span>') . '<br>';
        echo '<strong>Tutor LMS Aktif:</strong> ' . (function_exists('tutor_utils') ? '<span class="status-yes">✅ Evet</span>' : '<span class="status-no">❌ Hayır</span>') . '<br>';
        echo '<strong>Debug Modu:</strong> ' . (defined('WP_DEBUG') && WP_DEBUG ? '<span class="status-yes">✅ Aktif</span>' : '<span class="status-no">❌ Pasif</span>') . '<br>';
        echo '</div>';
        ?>
    </div>

    <div class="test-section">
        <h2>Tutor Ürünleri</h2>
        <?php
        // Get all products that are linked to Tutor courses
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_tutor_product',
                    'value' => 'yes',
                    'compare' => '='
                )
            )
        );
        
        $tutor_products = get_posts($args);
        
        if ($tutor_products) {
            echo '<table>';
            echo '<tr><th>Ürün ID</th><th>Ürün Adı</th><th>Kurs ID</th><th>Kurs Adı</th><th>Durum</th></tr>';
            
            foreach ($tutor_products as $product) {
                $product_id = $product->ID;
                $product_name = $product->post_title;
                
                // Get course ID using reflection to access private method
                $reflection = new ReflectionClass($integration);
                $method = $reflection->getMethod('get_course_id_from_product');
                $method->setAccessible(true);
                $course_id = $method->invoke($integration, $product_id);
                
                $course_name = $course_id ? get_the_title($course_id) : 'Bulunamadı';
                
                echo '<tr>';
                echo '<td>' . $product_id . '</td>';
                echo '<td>' . esc_html($product_name) . '</td>';
                echo '<td>' . ($course_id ?: 'N/A') . '</td>';
                echo '<td>' . esc_html($course_name) . '</td>';
                echo '<td>' . ($course_id ? '<span class="status-yes">✅ Bağlı</span>' : '<span class="status-no">❌ Bağlı Değil</span>') . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
        } else {
            echo '<div class="test-result error">Tutor LMS ile bağlantılı ürün bulunamadı.</div>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>Kullanıcı Testleri</h2>
        <?php
        $current_user = wp_get_current_user();
        if ($current_user->ID) {
            echo '<div class="test-result">';
            echo '<strong>Mevcut Kullanıcı:</strong> ' . $current_user->display_name . ' (ID: ' . $current_user->ID . ')<br>';
            
            // Test with first Tutor product if available
            if ($tutor_products) {
                $test_product = $tutor_products[0];
                $test_product_id = $test_product->ID;
                
                echo '<br><strong>Test Ürünü:</strong> ' . $test_product->post_title . ' (ID: ' . $test_product_id . ')<br>';
                
                // Test methods using reflection
                $reflection = new ReflectionClass($integration);
                
                $is_tutor_product_method = $reflection->getMethod('is_tutor_course_product');
                $is_tutor_product_method->setAccessible(true);
                $is_tutor_product = $is_tutor_product_method->invoke($integration, $test_product_id);
                
                $has_completed_order_method = $reflection->getMethod('user_has_completed_order');
                $has_completed_order_method->setAccessible(true);
                $has_completed_order = $has_completed_order_method->invoke($integration, $current_user->ID, $test_product_id);
                
                $is_enrolled_method = $reflection->getMethod('user_is_enrolled_in_course');
                $is_enrolled_method->setAccessible(true);
                $is_enrolled = $is_enrolled_method->invoke($integration, $current_user->ID, $test_product_id);
                
                $should_show_button_method = $reflection->getMethod('should_show_add_to_cart');
                $should_show_button_method->setAccessible(true);
                $should_show_button = $should_show_button_method->invoke($integration, $test_product_id);
                
                echo '<br><strong>Test Sonuçları:</strong><br>';
                echo '• Tutor Ürünü mü: ' . ($is_tutor_product ? '<span class="status-yes">✅ Evet</span>' : '<span class="status-no">❌ Hayır</span>') . '<br>';
                echo '• Tamamlanmış Sipariş: ' . ($has_completed_order ? '<span class="status-yes">✅ Var</span>' : '<span class="status-no">❌ Yok</span>') . '<br>';
                echo '• Kursa Kayıtlı: ' . ($is_enrolled ? '<span class="status-yes">✅ Evet</span>' : '<span class="status-no">❌ Hayır</span>') . '<br>';
                echo '• Sepete Ekle Butonu Gösterilsin: ' . ($should_show_button ? '<span class="status-yes">✅ Evet</span>' : '<span class="status-no">❌ Hayır</span>') . '<br>';
            }
            
            echo '</div>';
        } else {
            echo '<div class="test-result error">Kullanıcı giriş yapmamış.</div>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>Veritabanı Kontrolleri</h2>
        <?php
        global $wpdb;
        
        echo '<div class="test-result">';
        
        // Check HPOS
        $hpos_enabled = class_exists('Automattic\WooCommerce\Internal\DataStores\Orders\OrdersTableDataStore');
        $hpos_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_orders'") === $wpdb->prefix . 'wc_orders';
        
        echo '<strong>HPOS (High Performance Order Storage):</strong><br>';
        echo '• HPOS Sınıfı: ' . ($hpos_enabled ? '<span class="status-yes">✅ Mevcut</span>' : '<span class="status-no">❌ Mevcut Değil</span>') . '<br>';
        echo '• HPOS Tablosu: ' . ($hpos_table_exists ? '<span class="status-yes">✅ Mevcut</span>' : '<span class="status-no">❌ Mevcut Değil</span>') . '<br>';
        
        // Check Tutor tables
        $tutor_enrolled_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'tutor_enrolled'");
        echo '<br><strong>Tutor Verileri:</strong><br>';
        echo '• Toplam Enrollment: ' . $tutor_enrolled_count . '<br>';
        
        // Check WooCommerce orders
        if ($hpos_table_exists) {
            $completed_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}wc_orders WHERE status = 'wc-completed'");
        } else {
            $completed_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'shop_order' AND post_status = 'wc-completed'");
        }
        echo '• Tamamlanmış Siparişler: ' . $completed_orders . '<br>';
        
        echo '</div>';
        ?>
    </div>

    <div class="test-section">
        <h2>Hook Testleri</h2>
        <?php
        echo '<div class="test-result">';
        echo '<strong>Aktif Hook\'lar:</strong><br>';
        
        // Check if our hooks are registered
        global $wp_filter;
        
        $hooks_to_check = array(
            'woocommerce_single_product_summary' => 25,
            'woocommerce_is_purchasable' => 10,
            'wp_head' => 10
        );
        
        foreach ($hooks_to_check as $hook => $priority) {
            $hook_exists = isset($wp_filter[$hook]) && isset($wp_filter[$hook]->callbacks[$priority]);
            echo '• ' . $hook . ' (' . $priority . '): ' . ($hook_exists ? '<span class="status-yes">✅ Kayıtlı</span>' : '<span class="status-no">❌ Kayıtlı Değil</span>') . '<br>';
        }
        
        echo '</div>';
        ?>
    </div>

    <div class="test-section info">
        <h2>Test Tamamlandı</h2>
        <p>Bu test sayfası Tutor LMS entegrasyonunun doğru çalışıp çalışmadığını kontrol eder. Herhangi bir sorun varsa yukarıdaki bilgileri kullanarak debug yapabilirsiniz.</p>
        <p><strong>Not:</strong> Bu dosyayı production ortamında silmeyi unutmayın!</p>
    </div>

</body>
</html>
