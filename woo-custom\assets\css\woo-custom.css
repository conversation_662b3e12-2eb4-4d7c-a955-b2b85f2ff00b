/**
 * Woo Custom Plugin Styles
 */

/* Wishlist <PERSON><PERSON> Styles */
.woo-custom-wishlist-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Heart icon styles */
.woo-custom-wishlist-btn .heart-icon,
.woo-custom-wishlist-btn .heart-icon-filled {
    font-size: 20px;
    transition: all 0.3s ease;
    display: inline-block;
}

.woo-custom-wishlist-btn .heart-icon {
    color: #999;
}

.woo-custom-wishlist-btn .heart-icon-filled {
    color: #e74c3c;
    display: none;
}

/* Hover states */
.woo-custom-wishlist-btn:hover .heart-icon {
    color: #e74c3c;
    transform: scale(1.1);
}

/* Active state (in wishlist) */
.woo-custom-wishlist-btn.in-wishlist .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn.in-wishlist .heart-icon-filled {
    display: inline-block;
    animation: heartBeat 0.6s ease-in-out;
}

/* Heart beat animation */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Product loop positioning */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woocommerce ul.products li.product:hover .woo-custom-wishlist-btn {
    opacity: 1;
    transform: translateY(0);
}

/* Single product page wishlist button */
.woo-custom-wishlist-wrapper {
    margin: 15px 0;
}

.woo-custom-wishlist-btn.single-product {
    background: none;
    border: none;
    padding: 0;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    margin-left: 30px;
    white-space: nowrap;
    width: 110px;
    outline: none;
}

.woo-custom-wishlist-btn.single-product:focus {
    outline: none;
    box-shadow: none;
}

.woo-custom-wishlist-btn.single-product:hover {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product:hover .heart-icon {
    color: #e74c3c;
}

.woo-custom-wishlist-btn.single-product.in-wishlist {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product .wishlist-text {
    font-weight: 500;
    margin-left: 4px;
}

.woo-custom-wishlist-btn.single-product .heart-icon,
.woo-custom-wishlist-btn.single-product .heart-icon-filled {
    font-size: 18px;
    margin-right: 4px;
}

/* Loading state */
.woo-custom-wishlist-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.woo-custom-wishlist-btn.loading .heart-icon,
.woo-custom-wishlist-btn.loading .heart-icon-filled {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Login required state */
.woo-custom-wishlist-btn.requires-login {
    opacity: 0.7;
}

.woo-custom-wishlist-btn.requires-login:hover {
    opacity: 0.9;
}

/* Notification styles */
.woo-custom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.woo-custom-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.woo-custom-notification.success {
    background: #27ae60;
}

.woo-custom-notification.error {
    background: #e74c3c;
}

/* My Account menu item icons - More specific selectors */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    display: inline-block !important;
    margin-left: 4px !important;
    text-align: center !important;
    line-height: 24px !important;
    font-size: 20px !important;
    vertical-align: middle !important;
    font-style: normal !important;
    font-weight: normal !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before {
    content: "♡" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    content: "☆" !important;
}

/* Hover effects for menu icons */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before {
    content: "♥" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before {
    content: "★" !important;
}



/* Responsive design */
@media (max-width: 768px) {
    .woocommerce ul.products li.product .woo-custom-wishlist-btn {
        opacity: 1;
        transform: translateY(0);
        top: 5px;
        right: 5px;
        padding: 6px;
    }
    
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled {
        font-size: 18px;
    }
    
    .woo-custom-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .woo-custom-notification.show {
        transform: translateY(0);
    }
}

/* Product grid compatibility */
.woocommerce ul.products li.product {
    position: relative;
}

/* Theme compatibility adjustments */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    z-index: 10;
}

/* Ensure proper positioning in different themes */
.products .product .woo-custom-wishlist-btn,
.wc-block-grid__products .wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* WooCommerce Blocks compatibility */
.wc-block-grid__product {
    position: relative;
}

.wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: all 0.3s ease;
}

.wc-block-grid__product:hover .woo-custom-wishlist-btn {
    opacity: 1;
}

/* Accessibility improvements */
.woo-custom-wishlist-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon-filled {
    display: inline-block;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .woo-custom-wishlist-btn {
        border: 2px solid currentColor;
    }
    
    .woo-custom-wishlist-btn .heart-icon {
        color: currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .woo-custom-wishlist-btn,
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled,
    .woo-custom-notification {
        transition: none;
        animation: none;
    }
}

/* Course wishlist button styles */
.woo-custom-course-wishlist-wrapper {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.woo-custom-wishlist-btn.course-wishlist {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.tutor-course-card:hover .woo-custom-wishlist-btn.course-wishlist {
    opacity: 1;
    transform: translateY(0);
}

.woo-custom-wishlist-btn.course-wishlist .wishlist-text {
    display: none;
}

.woo-custom-wishlist-btn.single-course {
    background: none;
    border: none;
    padding: 0;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    margin-left: 30px;
    white-space: nowrap;
    width: auto;
    outline: none;
}

.woo-custom-wishlist-btn.single-course .wishlist-text {
    font-weight: 500;
    margin-left: 4px;
}

/* Hide Tutor LMS bookmark buttons */
.tutor-course-bookmark {
    display: none !important;
}

/* Course card positioning */
.tutor-course-card {
    position: relative;
}

/* Mobile responsiveness for course buttons */
@media (max-width: 768px) {
    .woo-custom-wishlist-btn.course-wishlist {
        opacity: 1;
        transform: translateY(0);
        top: 5px;
        right: 5px;
        width: 35px;
        height: 35px;
    }
}

/* Course Info Tab Styles */
.woo-custom-course-info {
    padding: 20px 0;
    font-family: inherit;
}

.woo-custom-course-info h3 {
    margin: 0 0 20px 0;
    padding: 0 0 10px 0;
    border-bottom: 2px solid #e1e1e1;
    font-size: 1.2em;
    font-weight: 600;
    color: #333;
}

.woo-custom-course-info h4 {
    margin: 0 0 8px 0;
    font-size: 1em;
    font-weight: 600;
    color: #555;
}

/* Course Overview Grid */
.course-meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.course-meta-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border-left: 4px solid #0073aa;
}

.course-meta-item .meta-icon {
    margin-right: 12px;
    color: #0073aa;
    font-size: 20px;
    line-height: 1;
}

.course-meta-item .meta-content h4 {
    margin: 0 0 5px 0;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #666;
}

.course-meta-item .meta-content p {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
}

.rating-average {
    color: #f39c12;
    font-weight: bold;
}

.rating-count {
    color: #666;
    font-size: 0.9em;
}

/* Instructor Section */
.instructor-info {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 30px;
}

.instructor-avatar .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.instructor-details h4 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    color: #333;
}

.instructor-bio {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

/* Video Section */
.video-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin-bottom: 30px;
}

.video-container iframe,
.video-container video {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Curriculum Section */
.curriculum-accordion {
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    overflow: hidden;
}

.curriculum-topic {
    border-bottom: 1px solid #e1e1e1;
}

.curriculum-topic:last-child {
    border-bottom: none;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f8f8;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.topic-header:hover {
    background: #f0f0f0;
}

.topic-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 1em;
    font-weight: 600;
    color: #333;
}

.topic-number {
    margin-right: 10px;
    color: #0073aa;
    font-weight: bold;
}

.topic-count {
    margin-left: 10px;
    font-size: 0.85em;
    color: #666;
    font-weight: normal;
}

.topic-toggle {
    color: #666;
    transition: transform 0.3s ease;
}

.topic-content {
    padding: 0;
    background: #fff;
}

.topic-contents {
    list-style: none;
    margin: 0;
    padding: 0;
}

.content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.content-item:last-child {
    border-bottom: none;
}

.content-item:hover {
    background: #f9f9f9;
}

.content-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.content-type-icon {
    color: #666;
    font-size: 16px;
}

.content-title {
    font-weight: 500;
    color: #333;
}

.content-type {
    font-size: 0.85em;
    color: #666;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 12px;
}

.preview-badge {
    background: #e74c3c;
    color: white;
    font-size: 0.75em;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.content-item.preview {
    background: #fff9f9;
}

.no-content {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
    margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .course-meta-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .course-meta-item {
        padding: 12px;
    }

    .instructor-info {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .topic-header {
        padding: 12px 15px;
    }

    .topic-title {
        font-size: 0.95em;
    }

    .content-item {
        padding: 10px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .content-info {
        width: 100%;
    }

    .preview-badge {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .woo-custom-course-info {
        padding: 15px 0;
    }

    .topic-count {
        display: none;
    }

    .content-type {
        display: none;
    }
}

/* Tutor LMS Integration Styles */
.woo-custom-already-purchased {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.already-purchased-message {
    color: #28a745;
    font-weight: 600;
    margin: 0 0 15px 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.already-purchased-message .dashicons {
    color: #28a745;
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.already-purchased-message .dashicons-clock {
    color: #f39c12;
}

.course-access-button {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 6px;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.course-access-button:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.course-access-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

/* Alternative styling for different themes */
.woo-custom-already-purchased.theme-alt {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #28a745;
}

.woo-custom-already-purchased.theme-alt .already-purchased-message {
    font-size: 18px;
    margin-bottom: 20px;
}

.woo-custom-already-purchased.theme-alt .course-access-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    padding: 15px 30px;
    font-size: 16px;
}

/* Mobile responsiveness for purchased message */
@media (max-width: 768px) {
    .woo-custom-already-purchased {
        padding: 20px 15px;
        margin: 15px 0;
    }

    .already-purchased-message {
        font-size: 15px;
        flex-direction: column;
        gap: 5px;
    }

    .course-access-button {
        padding: 10px 20px;
        font-size: 13px;
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .woo-custom-already-purchased {
        padding: 15px 10px;
    }

    .already-purchased-message {
        font-size: 14px;
    }

    .already-purchased-message .dashicons {
        font-size: 18px;
        width: 18px;
        height: 18px;
    }
}
