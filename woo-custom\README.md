# Woo Custom Plugin

A custom WordPress plugin that enhances WooCommerce functionality by adding Wishlist, My Reviews features to the My Account page, and Tutor LMS course information integration.

## Features

### 1. Wishlist Functionality
- **Heart Icon on Products**: Displays a heart icon when hovering over products in shop/category pages
- **Add/Remove from Wishlist**: Click the heart icon to add or remove products from personal wishlist
- **Wishlist Page**: Dedicated page in My Account showing all saved products
- **User-Specific Data**: Each user has their own separate wishlist stored in the database
- **Responsive Design**: Works on desktop and mobile devices

### 2. My Reviews Functionality
- **My Reviews Page**: Dedicated page in My Account showing all user's product reviews
- **Review Statistics**: Shows total reviews count and average rating
- **Verified Purchase Badges**: Displays verification status for purchases
- **Product Integration**: Links back to original products and reviews
- **Uses WooCommerce Infrastructure**: Leverages existing WooCommerce review system

### 3. Tutor LMS Course Information Integration
- **Course Info Tab**: Adds a "Kurs Bilgileri" (Course Information) tab to WooCommerce product pages for Tutor LMS products
- **Course Level Display**: Shows course difficulty level (Beginner/Intermediate/Advanced)
- **Enrolled Students Count**: Displays the number of enrolled students
- **Course Curriculum**: Shows detailed course curriculum with topics and lessons
- **Course Duration**: Displays total course duration
- **Course Rating**: Shows average rating and review count
- **Instructor Information**: Displays instructor details and bio
- **Course Video**: Shows course promotional video if available
- **Responsive Design**: Fully responsive design for all devices

### 4. Tutor LMS Add to Cart Button Control
- **Smart Button Visibility**: Automatically hides "Add to Cart" button for purchased or pending Tutor LMS courses
- **Multi-Status Detection**: Checks for completed, processing, and pending orders (matches Tutor LMS behavior)
- **Enrollment Integration**: Checks both completed and pending Tutor LMS enrollments
- **Status-Aware Messages**:
  - Completed purchases: "Bu kursa zaten sahipsiniz" with "Kursa Git" button
  - Pending orders: "Bu kurs için siparişiniz işleniyor" with explanation
- **Course Access Control**: "Kursa Git" button only appears for completed purchases
- **WooCommerce Compatibility**: Works with both HPOS and legacy order storage systems
- **Debug Mode**: Includes comprehensive debug information when WP_DEBUG is enabled

## Installation

1. Upload the `woo-custom` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Make sure WooCommerce is installed and activated

## Requirements

- WordPress 6.0 or higher
- WooCommerce 8.0 or higher
- PHP 7.4 or higher
- Tutor LMS (optional, for course information integration)

## File Structure

```
woo-custom/
├── woo-custom.php              # Main plugin file
├── includes/
│   ├── class-woo-custom-account.php    # My Account customizations
│   ├── class-woo-custom-wishlist.php   # Wishlist functionality
│   ├── class-woo-custom-reviews.php    # Reviews functionality
│   ├── class-woo-custom-ajax.php       # AJAX handlers
│   ├── class-woo-custom-course-info.php # Tutor LMS course info integration
│   └── class-woo-custom-tutor-integration.php # Tutor LMS add to cart control
├── templates/
│   ├── myaccount/
│   │   ├── wishlist.php        # Wishlist page template
│   │   └── my-reviews.php      # My Reviews page template
│   └── course-info-tab.php     # Course information tab template
├── assets/
│   ├── css/
│   │   └── woo-custom.css      # Plugin styles
│   └── js/
│       └── woo-custom.js       # Plugin JavaScript
├── languages/
│   └── woo-custom-tr_TR.po     # Turkish translations
├── test-course-info.php        # Course info integration test file
├── test-tutor-integration.php  # Tutor LMS integration test file
└── README.md                   # This file
```

## Database Tables

The plugin creates one custom table:

### `wp_woo_custom_wishlist`
- `id` (bigint): Primary key
- `user_id` (bigint): WordPress user ID
- `product_id` (bigint): WooCommerce product ID
- `date_added` (datetime): When item was added to wishlist

## Hooks and Filters

### Actions
- `woocommerce_after_shop_loop_item` - Adds wishlist button to product loops
- `woocommerce_single_product_summary` - Adds wishlist button to single product page
- `woocommerce_account_wishlist_endpoint` - Handles wishlist page content
- `woocommerce_account_my-reviews_endpoint` - Handles my reviews page content

### Filters
- `woocommerce_account_menu_items` - Adds custom menu items to My Account
- `woocommerce_endpoint_wishlist_title` - Sets wishlist page title
- `woocommerce_endpoint_my-reviews_title` - Sets my reviews page title

## AJAX Endpoints

### `woo_custom_toggle_wishlist`
Handles adding/removing products from wishlist.

**Parameters:**
- `product_id` (int): Product ID to toggle
- `nonce` (string): Security nonce

**Response:**
```json
{
    "success": true,
    "data": {
        "action": "added|removed",
        "message": "Success message",
        "count": 5
    }
}
```

## Customization

### Styling
The plugin includes comprehensive CSS that can be customized by:
1. Editing `assets/css/woo-custom.css`
2. Adding custom CSS to your theme
3. Using the WordPress Customizer

### Templates
Templates can be overridden by copying them to your theme:
```
your-theme/woocommerce/myaccount/wishlist.php
your-theme/woocommerce/myaccount/my-reviews.php
```

### Functionality
The plugin is built with hooks and filters for easy customization:

```php
// Customize wishlist button text
add_filter('woo_custom_wishlist_button_text', function($text) {
    return 'Save for Later';
});

// Customize reviews per page
add_filter('woo_custom_reviews_per_page', function($per_page) {
    return 20;
});
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Accessibility

The plugin includes:
- ARIA labels and attributes
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Reduced motion support

## Performance

- Optimized database queries
- Minimal JavaScript footprint
- CSS optimized for fast loading
- AJAX requests for smooth user experience

## Security

- Nonce verification for all AJAX requests
- User capability checks
- SQL injection prevention
- XSS protection

## Usage

### Tutor LMS Integration Setup

1. **Enable Tutor LMS Integration**: The integration is automatically enabled when both WooCommerce and Tutor LMS are active
2. **Link Products to Courses**: In WooCommerce product edit page, check "Tutor Product" option
3. **Test Integration**: Visit `/wp-content/plugins/woo-custom/test-tutor-integration.php` (admin only)

### How It Works

1. **Product Purchase Check**: When a user visits a WooCommerce product page for a Tutor course:
   - Plugin checks if user has a completed order for this product
   - Plugin also checks if user is enrolled in the associated course
   - If either condition is true, "Add to Cart" button is hidden

2. **Alternative Display**: Instead of "Add to Cart" button, users see:
   - "Bu kursa zaten sahipsiniz" (You already own this course) message
   - "Kursa Git" (Go to Course) button linking to the course page

3. **Debug Mode**: Enable `WP_DEBUG` to see detailed debug information on product pages

## Troubleshooting

### Wishlist buttons not appearing
1. Check if WooCommerce is active
2. Verify user is logged in
3. Check for JavaScript errors in browser console

### My Account pages not working
1. Go to Settings > Permalinks and click "Save Changes"
2. Check if custom endpoints are registered
3. Verify template files exist

### Tutor LMS integration not working
1. Verify both WooCommerce and Tutor LMS are active
2. Check if products are marked as "Tutor Product" in WooCommerce
3. Enable WP_DEBUG and check debug output on product pages
4. Run the test file: `/wp-content/plugins/woo-custom/test-tutor-integration.php`
5. Check if courses are properly linked to products

### Database issues
1. Deactivate and reactivate the plugin
2. Check database table creation
3. Verify user permissions

## Support

For support and bug reports, please contact the plugin developer.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.1.1
- **FIXED**: Tutor LMS integration now properly handles pending orders
- Button visibility now matches Tutor LMS behavior (hides for pending, processing, and completed orders)
- Enrollment check includes both completed and pending enrollments
- Status-aware messages: different messages for completed vs pending purchases
- "Go to Course" button only appears for completed purchases
- Enhanced debug information with separate pending/completed status checks

### Version 1.1.0
- Added Tutor LMS integration for "Add to Cart" button control
- Smart button visibility based on purchase history and enrollment status
- Support for both HPOS and legacy WooCommerce order storage
- Comprehensive debug mode for troubleshooting
- "Go to Course" button for purchased courses
- User-friendly purchase status messages
- Test file for integration verification

### Version 1.0.0
- Initial release
- Wishlist functionality
- My Reviews functionality
- My Account integration
- Responsive design
- Tutor LMS course information integration
- AJAX support
